#!/usr/bin/env python3
"""Test script for CambAI Toolkit integration."""

import sys
import os

# Add the langchain-community path
sys.path.insert(0, 'langchain-community/libs/community')

def test_cambai_toolkit():
    """Test the CambAI toolkit functionality."""
    try:
        print("🧪 Testing CambAI Toolkit Integration...")
        print("=" * 50)
        
        # Test 1: Import the toolkit
        print("1. Testing toolkit import...")
        from langchain_community.agent_toolkits.cambai import CambAIToolkit
        print("   ✅ CambAI Toolkit imported successfully!")
        
        # Test 2: Create toolkit instance
        print("2. Testing toolkit creation...")
        toolkit = CambAIToolkit(voice_id=20303)
        print("   ✅ Toolkit instance created successfully!")
        
        # Test 3: Get tools
        print("3. Testing tool retrieval...")
        tools = toolkit.get_tools()
        print(f"   ✅ Retrieved {len(tools)} tools")
        
        # Test 4: Examine tools
        print("4. Examining available tools...")
        for i, tool in enumerate(tools, 1):
            print(f"   Tool {i}: {tool.name}")
            print(f"   Description: {tool.description[:100]}...")
            print(f"   Type: {type(tool).__name__}")
            print()
        
        # Test 5: Test toolkit from main import
        print("5. Testing main toolkit import...")
        from langchain_community.agent_toolkits import CambAIToolkit as MainCambAIToolkit
        main_toolkit = MainCambAIToolkit()
        main_tools = main_toolkit.get_tools()
        print(f"   ✅ Main import works! {len(main_tools)} tools available")
        
        print("=" * 50)
        print("🎉 All tests passed! CambAI Toolkit is ready to use.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_example_usage():
    """Test example usage of the toolkit."""
    try:
        print("\n🚀 Testing Example Usage...")
        print("=" * 50)
        
        from langchain_community.agent_toolkits.cambai import CambAIToolkit
        
        # Create toolkit with custom voice
        toolkit = CambAIToolkit(voice_id=20304)
        tools = toolkit.get_tools()
        
        print(f"Created toolkit with voice ID 20304")
        print(f"Available tools: {[tool.name for tool in tools]}")
        
        # Test tool properties
        tts_tool = tools[0]  # Should be the text2speech tool
        print(f"TTS Tool name: {tts_tool.name}")
        print(f"TTS Tool voice_id: {tts_tool.voice_id}")
        
        print("✅ Example usage test completed!")
        
    except Exception as e:
        print(f"❌ Error in example usage: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    success = test_cambai_toolkit()
    if success:
        test_example_usage()
    else:
        sys.exit(1)
