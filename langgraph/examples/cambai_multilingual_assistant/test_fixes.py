#!/usr/bin/env python3
"""
Test script to verify the CambAI multilingual assistant fixes.
This script tests the key components independently.
"""

import os
import sys
import glob
from typing import Optional

def test_imports():
    """Test that all required imports work."""
    print("🧪 Testing imports...")
    try:
        from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
        from langchain_core.tools import tool
        from langchain_openai import ChatOpenAI
        from langgraph.graph import StateGraph, MessagesState, START, END
        from langgraph.checkpoint.memory import MemorySaver
        from langgraph.prebuilt import ToolNode, tools_condition
        from langchain_community.tools.cambai import CambAIText2SpeechTool
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_api_keys():
    """Test API key configuration."""
    print("🔑 Testing API keys...")
    
    camb_key = os.getenv("CAMB_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not camb_key or camb_key == "your-cambai-api-key-here":
        print("⚠️  CAMB_API_KEY not set or using placeholder")
        return False
    
    if not openai_key or openai_key == "your-openai-api-key-here":
        print("⚠️  OPENAI_API_KEY not set or using placeholder")
        return False
    
    print("✅ API keys are configured")
    return True

def test_cambai_tool():
    """Test CambAI tool initialization and basic functionality."""
    print("🔊 Testing CambAI tool...")
    
    try:
        from langchain_community.tools.cambai import CambAIText2SpeechTool
        
        # Initialize the tool
        cambai_tts = CambAIText2SpeechTool(voice_id=20303)
        print(f"✅ CambAI tool initialized: {cambai_tts.name}")
        
        # Test with a simple text
        print("🧪 Testing speech generation...")
        result = cambai_tts.invoke("Hello, this is a test of CambAI text to speech.")
        print(f"📝 CambAI result: {result}")
        
        # Check for generated audio files
        audio_files = glob.glob("*.wav") + glob.glob("cambai_*.wav")
        if audio_files:
            latest_audio = max(audio_files, key=os.path.getctime)
            print(f"🔊 Audio file generated: {latest_audio}")
            return True
        else:
            print("⚠️  No audio files found after generation")
            return False
            
    except Exception as e:
        print(f"❌ CambAI tool error: {e}")
        return False

def test_language_detection():
    """Test the language detection tool."""
    print("🌍 Testing language detection...")
    
    try:
        from langchain_core.tools import tool
        
        @tool
        def detect_language(text: str) -> str:
            """Detect the language of the given text."""
            language_patterns = {
                'spanish': ['hola', 'gracias', 'por favor'],
                'french': ['bonjour', 'merci', 'au revoir'],
                'german': ['hallo', 'danke', 'bitte'],
            }
            
            text_lower = text.lower()
            for language, patterns in language_patterns.items():
                if any(pattern in text_lower for pattern in patterns):
                    return language
            return 'english'
        
        # Test with different languages
        test_cases = [
            ("Hello world", "english"),
            ("Hola mundo", "spanish"),
            ("Bonjour le monde", "french"),
            ("Hallo Welt", "german"),
        ]
        
        for text, expected in test_cases:
            result = detect_language.invoke({"text": text})
            print(f"  '{text}' -> {result} (expected: {expected})")
            
        print("✅ Language detection working")
        return True
        
    except Exception as e:
        print(f"❌ Language detection error: {e}")
        return False

def test_workflow_creation():
    """Test LangGraph workflow creation."""
    print("🔄 Testing workflow creation...")
    
    if not test_api_keys():
        print("⚠️  Skipping workflow test due to missing API keys")
        return False
    
    try:
        from langchain_openai import ChatOpenAI
        from langgraph.graph import StateGraph, MessagesState, START, END
        from langgraph.prebuilt import ToolNode
        from langgraph.checkpoint.memory import MemorySaver
        
        # Create a minimal workflow
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7)
        
        def simple_agent(state):
            return {"messages": [llm.invoke(state["messages"])]}
        
        workflow = StateGraph(MessagesState)
        workflow.add_node("agent", simple_agent)
        workflow.add_edge(START, "agent")
        workflow.add_edge("agent", END)
        
        memory = MemorySaver()
        app = workflow.compile(checkpointer=memory)
        
        print("✅ Workflow created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Workflow creation error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Running CambAI Multilingual Assistant Tests")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("API Keys", test_api_keys),
        ("Language Detection", test_language_detection),
        ("CambAI Tool", test_cambai_tool),
        ("Workflow Creation", test_workflow_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The notebook should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("💡 Make sure to set proper API keys and install required packages.")

if __name__ == "__main__":
    main()
