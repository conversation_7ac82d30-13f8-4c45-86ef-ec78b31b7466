# Install required packages
# !pip install cambai langchain-community langgraph langchain-openai

import os
from typing import Annotated, Dict, List, Optional

# Set up API keys (make sure to set these in your environment)
# os.environ["CAMB_API_KEY"] = "your-cambai-api-key"
# os.environ["OPENAI_API_KEY"] = "your-openai-api-key"

# Verify API keys are set
if not os.getenv("CAMB_API_KEY"):
    print("⚠️  Please set CAMB_API_KEY environment variable")
if not os.getenv("OPENAI_API_KEY"):
    print("⚠️  Please set OPENAI_API_KEY environment variable")

!pip install cambai langchain-community "langgraph>=0.4.7" langchain-openai
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition

# Import CambAI tool
from langchain_community.tools.cambai import CambAIText2SpeechTool

from typing import TypedDict
from langgraph.graph import add_messages

class MultilingualAssistantState(MessagesState):
    """State for our multilingual assistant."""
    detected_language: Optional[str] = None
    audio_files: List[str] = []
    conversation_summary: Optional[str] = None

@tool
def detect_language(text: str) -> str:
    """Detect the language of the given text.
    
    Args:
        text: The text to analyze for language detection
        
    Returns:
        The detected language name
    """
    # Simple language detection based on common patterns
    # In a real implementation, you might use a proper language detection library
    language_patterns = {
        'spanish': ['hola', 'gracias', 'por favor', 'buenos días', 'buenas tardes', 'español'],
        'french': ['bonjour', 'merci', 's\'il vous plaît', 'au revoir', 'comment', 'français'],
        'german': ['hallo', 'danke', 'bitte', 'guten tag', 'auf wiedersehen', 'deutsch'],
        'italian': ['ciao', 'grazie', 'prego', 'buongiorno', 'arrivederci', 'italiano'],
        'portuguese': ['olá', 'obrigado', 'por favor', 'bom dia', 'tchau', 'português'],
        'hindi': ['नमस्ते', 'धन्यवाद', 'कृपया', 'अच्छा', 'हाँ', 'हिंदी'],
    }
    
    text_lower = text.lower()
    
    for language, patterns in language_patterns.items():
        if any(pattern in text_lower for pattern in patterns):
            return language
    
    return 'english'  # Default to English

os.environ["CAMB_API_KEY"] = "d144c37e-9028-46b2-b23b-3a2ab615959f"
cambai_tts = CambAIText2SpeechTool(
    voice_id=20303,
)  # You can experiment with different voice IDs
# Combine all tools
all_tools = [detect_language, cambai_tts]

print(f"Available tools: {[tool.name for tool in all_tools]}")


os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7)

# Bind tools to the model
llm_with_tools = llm.bind_tools(all_tools)

def should_continue(state: MessagesState) -> str:
    """Determine if we should continue to tools or end."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If the last message has tool calls, continue to tools
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    else:
        return END

def call_model(state: MessagesState) -> MessagesState:
    """Generate a response using the language model."""
    messages = state["messages"]
    
    # Create a system message that instructs the model to be multilingual
    system_prompt = """
    You are a helpful multilingual assistant powered by CambAI's text-to-speech technology.
    
    Instructions:
    1. First, detect the language of the user's input using the detect_language tool
    2. Respond in the same language as the user's input
    3. Be culturally appropriate and helpful
    4. After providing your response, convert it to speech using the camb_ai_text2speech tool
    5. Keep your responses concise but informative
    
    You support over 140 languages through CambAI's advanced speech synthesis.
    """
    
    # Add system message if it's not already there
    if not messages or not any("multilingual assistant" in str(msg.content) for msg in messages if hasattr(msg, 'content')):
        enhanced_messages = [HumanMessage(content=system_prompt)] + messages
    else:
        enhanced_messages = messages
    
    # Generate response
    response = llm_with_tools.invoke(enhanced_messages)
    
    return {"messages": [response]}

# Create tool node
tool_node = ToolNode(all_tools)

# Create the graph
workflow = StateGraph(MessagesState)

# Add nodes
workflow.add_node("agent", call_model)
workflow.add_node("tools", tool_node)

# Add edges
workflow.add_edge(START, "agent")
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {
        "tools": "tools",
        END: END,
    },
)
workflow.add_edge("tools", "agent")

# Compile the graph with memory
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

print("✅ Multilingual assistant workflow created successfully!")

# Initialize the language model
from langgraph.prebuilt import ToolNode
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7)

# Bind tools to the model
llm_with_tools = llm.bind_tools(all_tools)

def should_continue(state: MessagesState) -> str:
    """Determine if we should continue to tools or end."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If the last message has tool calls, continue to tools
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    else:
        return END

def call_model(state: MessagesState) -> MessagesState:
    """Generate a response using the language model."""
    messages = state["messages"]
    
    # Create a system message that instructs the model to be multilingual
    system_prompt = """
    You are a helpful multilingual assistant powered by CambAI's text-to-speech technology.
    
    Instructions:
    1. First, detect the language of the user's input using the detect_language tool
    2. Respond in the same language as the user's input
    3. Be culturally appropriate and helpful
    4. After providing your response, convert it to speech using the camb_ai_text2speech tool
    5. Keep your responses concise but informative
    
    You support over 140 languages through CambAI's advanced speech synthesis.
    """
    
    # Add system message if it's not already there
    if not messages or not any("multilingual assistant" in str(msg.content) for msg in messages if hasattr(msg, 'content')):
        enhanced_messages = [HumanMessage(content=system_prompt)] + messages
    else:
        enhanced_messages = messages
    
    # Generate response
    response = llm_with_tools.invoke(enhanced_messages)
    
    return {"messages": [response]}

# Create tool node
tool_node = ToolNode(all_tools)

# Create the graph
workflow = StateGraph(MessagesState)

# Add nodes
workflow.add_node("agent", call_model)
workflow.add_node("tools", tool_node)

# Add edges
workflow.add_edge(START, "agent")
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {
        "tools": "tools",
        END: END,
    },
)
workflow.add_edge("tools", "agent")

# Compile the graph with memory
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

print("✅ Multilingual assistant workflow created successfully!")

try:
    from IPython.display import Image, display
    display(Image(app.get_graph().draw_mermaid_png()))
except Exception as e:
    print(f"Could not display graph: {e}")
    print("Graph structure:")
    print(app.get_graph().draw_ascii())

# Update the CambAIText2SpeechTool initialization with better error handling
cambai_tts = CambAIText2SpeechTool(
    voice_id=20303,
)

# Add a function to check if the audio file exists and display its details
def check_audio_file(file_path="cambai_speech.wav"):
    """Check if the audio file exists and display its details."""
    import os
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        print(f"✅ Audio file exists at {file_path} (Size: {file_size} bytes)")
        return True
    else:
        print(f"❌ Audio file not found at {file_path}")
        return False

# Modify the test function to check for the audio file
def test_multilingual_assistant(user_input: str, thread_id: str = "test-thread"):
    """Test the multilingual assistant with a given input."""
    print(f"\n🗣️  User: {user_input}")
    print("=" * 50)
    
    config = {"configurable": {"thread_id": thread_id}}
    
    audio_files = []
    
    # Stream the response
    for event in app.stream(
        {"messages": [HumanMessage(content=user_input)]},
        config=config,
        stream_mode="values"
    ):
        if "messages" in event and event["messages"]:
            last_message = event["messages"][-1]
            if isinstance(last_message, AIMessage) and last_message.content:
                print(f"🤖 Assistant: {last_message.content}")
            elif isinstance(last_message, ToolMessage):
                if "cambai_speech.wav" in last_message.content:
                    file_path = last_message.content
                    print(f"🔊 Audio generated: {file_path}")
                    audio_files.append(file_path)
                    check_audio_file(file_path)
                elif last_message.name == "detect_language":
                    print(f"🌍 Language detected: {last_message.content}")
    
    print("=" * 50)
    
    # Check all audio files after completion
    if audio_files:
        for file_path in audio_files:
            check_audio_file(file_path)
    else:
        print("❌ No audio files were generated during this conversation")

test_multilingual_assistant(
    "Hello! Can you tell me about the weather today?",
    "english-thread"
)

test_multilingual_assistant(
    "¡Hola! ¿Puedes contarme un chiste divertido?",
    "spanish-thread"
)

test_multilingual_assistant(
    "Bonjour! Comment allez-vous aujourd'hui?",
    "french-thread"
)

test_multilingual_assistant(
    "Guten Tag! Können Sie mir bei der Planung einer Reise helfen?",
    "german-thread"
)



# Example of how to play audio (requires pygame)
def play_latest_audio():
    """Play the most recently generated audio file."""
    try:
        import os
        
        # Check if audio file exists
        audio_file = "cambai_speech.wav"
        if os.path.exists(audio_file):
            print(f"🔊 Playing audio: {audio_file}")
            cambai_tts.play(audio_file)
            print("✅ Audio playback completed")
        else:
            print("❌ No audio file found. Generate some speech first!")
    except ImportError:
        print("❌ pygame not installed. Install with: pip install pygame")
    except Exception as e:
        print(f"❌ Error playing audio: {e}")

# Uncomment to play the latest generated audio
play_latest_audio()

def show_conversation_history(thread_id: str):
    """Display the conversation history for a given thread."""
    config = {"configurable": {"thread_id": thread_id}}
    
    try:
        # Get the current state
        current_state = app.get_state(config)
        
        print(f"\n📜 Conversation History for Thread: {thread_id}")
        print("=" * 60)
        
        if current_state.values and "messages" in current_state.values:
            messages = current_state.values["messages"]
            
            for i, message in enumerate(messages, 1):
                if isinstance(message, HumanMessage):
                    print(f"{i}. 🗣️  User: {message.content[:100]}...")
                elif isinstance(message, AIMessage):
                    print(f"{i}. 🤖 Assistant: {message.content[:100]}...")
                elif isinstance(message, ToolMessage):
                    print(f"{i}. 🔧 Tool ({message.name}): {message.content[:50]}...")
        else:
            print("No conversation history found.")
            
        print("=" * 60)
        
    except Exception as e:
        print(f"Error retrieving conversation history: {e}")

# Show history for Spanish thread
# show_conversation_history("spanish-thread")

# Example of using different voice IDs
def create_assistant_with_voice(voice_id: int):
    """Create an assistant with a specific voice ID."""
    custom_tts = CambAIText2SpeechTool(voice_id=voice_id)
    custom_tools = [detect_language, custom_tts]
    
    print(f"Created assistant with voice ID: {voice_id}")
    return custom_tools

# Try different voices (you can experiment with different IDs)
voice_options = [20303, 20304, 20305]  # Example voice IDs
for voice_id in voice_options:
    tools = create_assistant_with_voice(voice_id)
    print(f"Voice {voice_id}: {len(tools)} tools available")

# Example of enhanced language detection (requires langdetect)
def enhanced_language_detection():
    """
    Example of how to use a more sophisticated language detection library.
    
    To use this, install: pip install langdetect
    """
    try:
        from langdetect import detect, detect_langs
        
        @tool
        def advanced_detect_language(text: str) -> str:
            """Detect language using advanced detection library."""
            try:
                detected = detect(text)
                confidence_scores = detect_langs(text)
                
                print(f"Detected: {detected}")
                print(f"Confidence scores: {confidence_scores}")
                
                return detected
            except:
                return "en"  # Default to English
                
        return advanced_detect_language
        
    except ImportError:
        print("langdetect not installed. Using basic detection.")
        return detect_language

# enhanced_lang_tool = enhanced_language_detection()